<?php

return [
    'logout_success' => 'Logged out successfully!',
    'language_updated' => 'Language updated successfully!',
    'account_not_exist' => 'Account does not exist',
    'incorrect_password' => 'Incorrect password',
    'auth_failed' => 'Authentication failed',
    'profile_updated' => 'Profile updated successfully',

    // API Messages
    'notifications_retrieved' => 'Notifications retrieved successfully',
    'dashboard_stats_retrieved' => 'Dashboard statistics retrieved successfully',
    'vehicles_retrieved' => 'Vehicles retrieved successfully',
    'vehicle_retrieved' => 'Vehicle retrieved successfully',
    'vehicle_events_retrieved' => 'Vehicle events retrieved successfully',
    'vehicle_not_found' => 'Vehicle not found',
    'error_occurred' => 'An error occurred',

    // Fleet Monitoring API Messages
    'fleet_vehicles_retrieved' => 'Fleet vehicles retrieved successfully',
    'vehicle_details_retrieved' => 'Vehicle details retrieved successfully',
    'vehicle_history_retrieved' => 'Vehicle history retrieved successfully',

    // Password Reset Messages
    'password_reset_email' => 'Password Reset Request',
    'password_reset_link_sent' => 'Password reset link has been sent to your email',
    'password_reset_success' => 'Password has been reset successfully',
    'password_reset_success_message' => 'Your password has been successfully reset. You can now return to the mobile app and log in with your new password.',
    'return_to_app' => 'Return to App',
    'invalid_or_expired_token' => 'Invalid or expired reset token',
    'user_not_found' => 'User not found',

    // Status
    'active' => 'Active',
    'inactive' => 'Inactive',

    // Vehicle Types
    'vehicle_types' => [
        'car' => 'Car',
        'truck' => 'Truck',
        'van' => 'Van',
        'motorcycle' => 'Motorcycle',
        'bus' => 'Bus',
        'trailer' => 'Trailer',
        'other' => 'Other',
    ],

    // Maintenance Status
    'maintenance_status' => [
        'pending' => 'Pending',
        'in_progress' => 'In Progress',
        'completed' => 'Completed',
        'cancelled' => 'Cancelled',
    ],
];
