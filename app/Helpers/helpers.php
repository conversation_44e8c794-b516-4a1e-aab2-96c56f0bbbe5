<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

// json response helper
function jsonResponse($status, $data = [], $statusCode = 200)
{
    return response()->json(array_merge([
        'status' => $status,
    ], $data), $statusCode);
}

// validation error response helper
function validationError($errors = [], $message = 'Validation Error', $statusCode = 400)
{
    // If errors are provided, get the first error message.
    if (!empty($errors) && is_object($errors) && $errors->first()) {
        $message = $errors->first(); // Set the message to the first error
    }

    return response()->json([
        'status' => false,
        'message' => $message,
        'errors' => $errors
    ], $statusCode);
}



/**
 * Get address from coordinates using geocoding API with caching
 */
function getAddressFromCoordinates($lat, $lng)
{
    try {
        if (empty($lat) || empty($lng)) {
            return $lat . ', ' . $lng;
        }

        // Create cache key
        $cacheKey = "geocode_" . round($lat, 6) . "_" . round($lng, 6);

        // Try to get from cache first (cache for 24 hours)
        return Cache::remember($cacheKey, 86400, function () use ($lat, $lng) {
            $api = "http://**************/nominatim/reverse?lat={$lat}&lon={$lng}&format=json&addressdetails=1&accept-language=it";

            $response = Http::timeout(5)->get($api);

            if ($response->successful()) {
                $data = $response->json();
                return $data['display_name'] ?? "{$lat}, {$lng}";
            }

            return "{$lat}, {$lng}";
        });
    } catch (\Exception $e) {
        Log::warning('Geocoding failed: ' . $e->getMessage());
        return "{$lat}, {$lng}";
    }
}


/**
 * Calculate stop duration between two timestamps
 */
function calculateStopDuration($startTime, $endTime = null, $format = true)
{
    try {
        $start = is_string($startTime) ?  parseFlexibleTimestamp($startTime) : $startTime;
        $end = $endTime === null || $endTime === 'Ongoing' ? now() : (is_string($endTime) ? parseFlexibleTimestamp($endTime) : $endTime);

        $diffInMinutes = $start->diffInMinutes($end);

        return $format ? formatDuration($diffInMinutes) : $diffInMinutes;
    } catch (\Exception $e) {
        Log::warning('Duration calculation failed: ' . $e->getMessage());
        return '0 minutes';
    }
}

/**
 * Format duration from minutes to human readable format
 */
function formatDuration($minutes)
{
    if ($minutes < 60) {
        return $minutes . ' minutes';
    }

    $hours = floor($minutes / 60);
    $remainingMinutes = $minutes % 60;

    if ($remainingMinutes == 0) {
        return $hours . ' hours';
    }

    return $hours . ' hours ' . $remainingMinutes . ' minutes';
}

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 */
function calculateGpsDistance($lat1, $lon1, $lat2, $lon2)
{
    $earthRadius = 6371; // Earth's radius in kilometers

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat / 2) * sin($dLat / 2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon / 2) * sin($dLon / 2);
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

    return $earthRadius * $c; // Distance in kilometers
}

/**
 * Calculate trip distance using odometer readings
 */
function calculateTripDistance($startOdometer, $endOdometer)
{
    $distance = max(0, ($endOdometer - $startOdometer)) / 1000; // Convert to km
    return round($distance, 2);
}

/**
 * Calculate total GPS distance from history data
 */
function calculateTotalGpsDistance($historyData)
{
    $totalDistance = 0;
    $lastLat = null;
    $lastLon = null;

    foreach ($historyData as $data) {
        if (isset($data['latitude']) && isset($data['longitude'])) {
            $currentLat = (float)$data['latitude'];
            $currentLon = (float)$data['longitude'];

            if ($lastLat !== null && $lastLon !== null) {
                $distance = calculateGpsDistance($lastLat, $lastLon, $currentLat, $currentLon);

                // Only add reasonable distances (less than 50km between consecutive points)
                if ($distance < 50) {
                    $totalDistance += $distance;
                }
            }

            $lastLat = $currentLat;
            $lastLon = $currentLon;
        }
    }

    return $totalDistance;
}

/**
 * Analyze trip events using eventID 250 and fallback methods
 * Returns consistent trip calculation logic for the entire platform
 */
function analyzeTripEvents($historyData, $date = null)
{
    $trips = [];
    $stops = [];
    $firstValidData = null;
    $lastValidData = null;

    // Variables for tracking trips and stops
    $lastStatus = null;         // For eventID 250 tracking
    $tripStartTime = null;      // When a trip begins
    $lastTripStop = null;       // For backup method tracking
    $currentStopStart = null;   // For speed/ignition based detection
    $tripStartLocation = null;  // Trip start location data
    $currentTripStart = null;   // Current trip start time

    foreach ($historyData as $data) {
        // Skip invalid data
        if (!isset($data['latitude']) || !isset($data['longitude']) || !isset($data['last_update'])) {
            continue;
        }

        // Set first and last valid data
        if ($firstValidData === null) {
            $firstValidData = $data;
        }
        $lastValidData = $data;

        // Parse common values
        $timestamp = parseFlexibleTimestamp($data['last_update']);
        $speed = isset($data['speed']) ? (float)$data['speed'] : 0;
        $ignition = isset($data['239']) ? (int)$data['239'] : 0;
        $movement = isset($data['240']) ? (int)$data['240'] : 0;
        $currentOdometer = getOdometerValue($data);
        $currentFuel = getFuelConsumption($data);

        // Method 1: Track stops and trips using eventID 250 (Primary method)
        if (isset($data['eventID']) && $data['eventID'] == 250 && isset($data['250'])) {
            $currentStatus = (int)$data['250'];

            // Trip started (250 = 1)
            if ($currentStatus === 1 && $lastStatus !== 1) {
                $tripStartTime = $timestamp;
                $tripStartLocation = $data;
                $currentTripStart = $timestamp;

                // If there was a previous stop, end it
                if ($lastTripStop !== null) {
                    $stopDuration = $lastTripStop['timestamp']->diffInMinutes($timestamp);

                    if ($stopDuration > 1) {
                        $stops[] = [
                            'start_time' => $lastTripStop['timestamp'],
                            'end_time' => $timestamp,
                            'duration' => formatDuration($stopDuration),
                            'duration_minutes' => $stopDuration,
                            'location' => [
                                'lat' => $lastTripStop['latitude'],
                                'lng' => $lastTripStop['longitude'],
                                'address' => getAddressFromCoordinates($lastTripStop['latitude'], $lastTripStop['longitude'])
                            ],
                            'odometer' => $currentOdometer,
                            'fuel' => $currentFuel,
                            'trip_distance' => calculateTripDistance($lastTripStop['odometer'], $currentOdometer)
                        ];
                    }
                    $lastTripStop = null;
                }
            }
            // Trip ended (250 = 0)
            elseif ($currentStatus === 0 && $lastStatus !== 0) {
                if ($tripStartTime !== null && $tripStartLocation !== null) {
                    $tripDuration = $tripStartTime->diffInMinutes($timestamp);

                    // Only add trips with duration greater than 1 minute
                    if ($tripDuration > 1) {
                        // Calculate trip distance using GPS coordinates
                        $tripDistance = calculateGpsDistance(
                            $tripStartLocation['latitude'],
                            $tripStartLocation['longitude'],
                            $data['latitude'],
                            $data['longitude']
                        );

                        // If GPS distance is too small, calculate based on odometer
                        if ($tripDistance < 0.1) {
                            $startOdo = getOdometerValue($tripStartLocation);
                            $endOdo = getOdometerValue($data);
                            $tripDistance = calculateTripDistance($startOdo, $endOdo);
                        }

                        // Calculate fuel consumption
                        $fuelConsumption = calculateFuelConsumption($tripStartLocation, $data);

                        $trips[] = [
                            'start_time' => $tripStartTime,
                            'end_time' => $timestamp,
                            'duration' => formatDuration($tripDuration),
                            'duration_minutes' => $tripDuration,
                            'start_location' => [
                                'lat' => $tripStartLocation['latitude'],
                                'lng' => $tripStartLocation['longitude'],
                                'address' => getAddressFromCoordinates($tripStartLocation['latitude'], $tripStartLocation['longitude'])
                            ],
                            'end_location' => [
                                'lat' => $data['latitude'],
                                'lng' => $data['longitude'],
                                'address' => getAddressFromCoordinates($data['latitude'], $data['longitude'])
                            ],
                            'distance' => $tripDistance,
                            'fuel_consumption' => $fuelConsumption
                        ];
                    }
                }

                // Mark stop start
                $lastTripStop = array_merge($data, ['timestamp' => $timestamp, 'odometer' => $currentOdometer]);
                $tripStartTime = null;
                $tripStartLocation = null;
                $currentTripStart = null;
            }

            $lastStatus = $currentStatus;
        }
        // Method 2: Fallback using speed and ignition (when eventID 250 is not available)
        else {
            $isMoving = ($speed > 1 && $ignition === 1) || $movement === 1;

            if ($isMoving && $currentStopStart === null && $currentTripStart === null) {
                // Trip started
                $currentTripStart = $timestamp;
                $tripStartLocation = $data;
            } elseif (!$isMoving && $currentTripStart !== null) {
                // Trip ended
                $tripDuration = $currentTripStart->diffInMinutes($timestamp);

                if ($tripDuration > 1) {
                    $trips[] = [
                        'start_time' => $currentTripStart,
                        'end_time' => $timestamp,
                        'duration' => formatDuration($tripDuration),
                        'duration_minutes' => $tripDuration,
                        'start_location' => [
                            'lat' => $tripStartLocation['latitude'],
                            'lng' => $tripStartLocation['longitude'],
                            'address' => getAddressFromCoordinates($tripStartLocation['latitude'], $tripStartLocation['longitude'])
                        ],
                        'end_location' => [
                            'lat' => $data['latitude'],
                            'lng' => $data['longitude'],
                            'address' => getAddressFromCoordinates($data['latitude'], $data['longitude'])
                        ]
                    ];
                }
                $currentTripStart = null;
                $tripStartLocation = null;
                $currentStopStart = $timestamp;
            } elseif ($isMoving && $currentStopStart !== null) {
                // Stop ended, trip started
                $stopDuration = $currentStopStart->diffInMinutes($timestamp);

                if ($stopDuration > 1) {
                    $stops[] = [
                        'start_time' => $currentStopStart,
                        'end_time' => $timestamp,
                        'duration' => formatDuration($stopDuration),
                        'duration_minutes' => $stopDuration,
                        'location' => [
                            'lat' => $data['latitude'],
                            'lng' => $data['longitude'],
                            'address' => getAddressFromCoordinates($data['latitude'], $data['longitude'])
                        ],
                        'odometer' => $currentOdometer,
                        'fuel' => $currentFuel
                    ];
                }
                $currentStopStart = null;
                $currentTripStart = $timestamp;
                $tripStartLocation = $data;
            }
        }
    }

    // Handle ongoing trips/stops at the end of the day
    $endOfDay = $date ? Carbon::createFromFormat('d-m-Y', $date)->endOfDay() : now();

    // Handle ongoing stop
    if ($lastTripStop !== null) {
        $stopDuration = $lastTripStop['timestamp']->diffInMinutes($endOfDay);

        if ($stopDuration > 1) {
            $stops[] = [
                'start_time' => $lastTripStop['timestamp'],
                'end_time' => 'Ongoing',
                'duration' => formatDuration($stopDuration),
                'duration_minutes' => $stopDuration,
                'location' => [
                    'lat' => $lastTripStop['latitude'],
                    'lng' => $lastTripStop['longitude'],
                    'address' => getAddressFromCoordinates($lastTripStop['latitude'], $lastTripStop['longitude'])
                ],
                'odometer' => getOdometerValue($lastValidData),
                'fuel' => getFuelConsumption($lastValidData)
            ];
        }
    }

    // Handle ongoing trip
    if ($currentTripStart !== null && $tripStartLocation !== null) {
        $tripDuration = $currentTripStart->diffInMinutes($endOfDay);

        if ($tripDuration > 1) {
            $tripDistance = calculateGpsDistance(
                $tripStartLocation['latitude'],
                $tripStartLocation['longitude'],
                $lastValidData['latitude'],
                $lastValidData['longitude']
            );

            if ($tripDistance < 0.1) {
                $startOdo = getOdometerValue($tripStartLocation);
                $endOdo = getOdometerValue($lastValidData);
                $tripDistance = calculateTripDistance($startOdo, $endOdo);
            }

            $fuelConsumption = calculateFuelConsumption($tripStartLocation, $lastValidData);

            $trips[] = [
                'start_time' => $currentTripStart,
                'end_time' => 'Ongoing',
                'duration' => formatDuration($tripDuration),
                'duration_minutes' => $tripDuration,
                'start_location' => [
                    'lat' => $tripStartLocation['latitude'],
                    'lng' => $tripStartLocation['longitude'],
                    'address' => getAddressFromCoordinates($tripStartLocation['latitude'], $tripStartLocation['longitude'])
                ],
                'end_location' => [
                    'lat' => $lastValidData['latitude'],
                    'lng' => $lastValidData['longitude'],
                    'address' => getAddressFromCoordinates($lastValidData['latitude'], $lastValidData['longitude'])
                ],
                'distance' => $tripDistance,
                'fuel_consumption' => $fuelConsumption
            ];
        }
    }

    return [
        'trips' => $trips,
        'stops' => $stops,
        'first_data' => $firstValidData,
        'last_data' => $lastValidData
    ];
}

/**
 * Calculate fuel consumption between two data points
 */
function calculateFuelConsumption($startData, $endData)
{
    $startFuel = getFuelConsumption($startData);
    $endFuel = getFuelConsumption($endData);

    // If end fuel is less than start fuel, fuel was consumed
    if ($endFuel < $startFuel) {
        return $startFuel - $endFuel;
    }
    // If end fuel is greater, it might be a refill or sensor error
    else {
        // Calculate distance for estimation
        if (isset($startData['latitude']) && isset($endData['latitude'])) {
            $distance = calculateGpsDistance(
                $startData['latitude'],
                $startData['longitude'],
                $endData['latitude'],
                $endData['longitude']
            );
            // Use a reasonable estimate based on distance (0.05L per km)
            return $distance * 0.05;
        }
        return 0;
    }
}

/**
 * Analyze fuel events to detect consumption and refueling
 */
function analyzeFuelEvents($historyData)
{
    $totalConsumption = 0;
    $totalRefueling = 0;
    $lastFuelLevel = null;

    foreach ($historyData as $data) {
        $currentFuelLevel = getFuelConsumption($data);

        if ($lastFuelLevel !== null && $currentFuelLevel != $lastFuelLevel) {
            $fuelDifference = $currentFuelLevel - $lastFuelLevel;

            // If fuel level increased, it's a refueling event
            if ($fuelDifference > 0) {
                // Only count significant refueling (more than 1 liter)
                if ($fuelDifference > 1) {
                    $totalRefueling += $fuelDifference;
                }
            } else {
                // Fuel level decreased, it's consumption
                // Only count reasonable consumption (less than 20 liters at once)
                if (abs($fuelDifference) < 20) {
                    $totalConsumption += abs($fuelDifference);
                }
            }
        }

        $lastFuelLevel = $currentFuelLevel;
    }

    return [
        'consumption' => $totalConsumption,
        'refueling' => $totalRefueling
    ];
}

/**
 * Calculate total stop duration from stops array
 */
function calculateTotalStopDuration($stops)
{
    $totalMinutes = 0;

    foreach ($stops as $stop) {
        if (isset($stop['duration_minutes'])) {
            $totalMinutes += $stop['duration_minutes'];
        } elseif (isset($stop['duration'])) {
            // Parse duration string like "2 hours 30 minutes"
            preg_match('/(\d+)\s*hours?\s*(\d+)\s*minutes?/', $stop['duration'], $matches);
            if (count($matches) === 3) {
                $totalMinutes += ($matches[1] * 60) + $matches[2];
            } else {
                preg_match('/(\d+)\s*minutes?/', $stop['duration'], $matches);
                if (count($matches) === 2) {
                    $totalMinutes += $matches[1];
                }
            }
        }
    }

    return formatDuration($totalMinutes);
}

/**
 * Calculate total trip duration from trips array
 */
function calculateTotalTripDuration($trips)
{
    $totalMinutes = 0;

    foreach ($trips as $trip) {
        if (isset($trip['duration_minutes'])) {
            $totalMinutes += $trip['duration_minutes'];
        } elseif (isset($trip['duration'])) {
            // Parse duration string like "2 hours 30 minutes"
            preg_match('/(\d+)\s*hours?\s*(\d+)\s*minutes?/', $trip['duration'], $matches);
            if (count($matches) === 3) {
                $totalMinutes += ($matches[1] * 60) + $matches[2];
            } else {
                preg_match('/(\d+)\s*minutes?/', $trip['duration'], $matches);
                if (count($matches) === 2) {
                    $totalMinutes += $matches[1];
                }
            }
        }
    }

    return formatDuration($totalMinutes);
}

/**
 * Distribute total distance among stops proportionally
 */
function distributeDistanceToStops(&$stops, $totalDistance)
{
    if (empty($stops) || $totalDistance <= 0) {
        return;
    }

    $totalDuration = 0;
    foreach ($stops as $stop) {
        $totalDuration += $stop['duration_minutes'] ?? 0;
    }

    if ($totalDuration > 0) {
        foreach ($stops as &$stop) {
            $stopDuration = $stop['duration_minutes'] ?? 0;
            $proportionalDistance = ($stopDuration / $totalDuration) * $totalDistance;
            $stop['proportional_distance'] = round($proportionalDistance, 2);
        }
    }
}

/**
 * Get vehicle mileage based on vehicle type for fuel estimation
 */
function getMileageForVehicle($vehicleType)
{
    $mileageMap = [
        'car' => 15,        // 15 km/L
        'truck' => 8,       // 8 km/L
        'van' => 12,        // 12 km/L
        'motorcycle' => 25, // 25 km/L
        'bus' => 6,         // 6 km/L
    ];

    return $mileageMap[strtolower($vehicleType)] ?? 10; // Default 10 km/L
}

/**
 * Calculate daily odometer reading with proper handling of initial readings
 */
function calculateDailyOdometer($currentOdometer, $previousDayOdometer, $initialReading = 0)
{
    $currentTotal = $currentOdometer + $initialReading;
    $previousTotal = $previousDayOdometer + $initialReading;

    return $currentTotal > $previousTotal ? $currentTotal - $previousTotal : 0;
}


// In a helper file
function parseFlexibleTimestamp($timestamp, $formats = ['d/m/Y H:i:s', 'd/m/Y H:i']) {
    foreach ($formats as $format) {
        try {
            return Carbon::createFromFormat($format, $timestamp);
        } catch (\Exception $e) {
            continue;
        }
    }
    return null;
}
